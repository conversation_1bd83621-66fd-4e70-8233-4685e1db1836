<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <title>Chess AI - Melawan Engine</title>
    <!-- Impor chess.js dari CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.2/chess.js"></script>
    <style>
        body { font-family: sans-serif; display: flex; justify-content: center; align-items: flex-start; margin-top: 20px; }
        #game-container { display: flex; flex-direction: column; align-items: center; }
        #chessboard { width: 400px; height: 400px; border: 2px solid black; display: grid; grid-template-columns: repeat(8, 1fr); }
        .square { width: 50px; height: 50px; display: flex; justify-content: center; align-items: center; position: relative; box-sizing: border-box; }
        .white { background-color: #f0d9b5; }
        .black { background-color: #b58863; }
        .piece { width: 45px; height: 45px; background-size: contain; background-repeat: no-repeat; cursor: pointer; }
        .selected { border: 3px solid #ffcc00; }
        #game-info { text-align: center; margin-top: 15px; }
        #newGame, #flipBoard { padding: 8px 15px; font-size: 16px; cursor: pointer; margin-left: 5px; }
        .coordinate {
            position: absolute;
            font-size: 10px;
            color: #333;
            pointer-events: none;
        }
        .rank { top: 2px; left: 2px; }
        .file { bottom: 2px; right: 2px; }
    #color-selection { margin: 10px 0; }
        </style>
</head>
<body>
    <div id="game-container">
        <div id="chessboard"></div>
            <div id="color-selection">
                <label><input type="radio" name="color" value="w" checked> Putih</label>
                <label style="margin-left:10px;"><input type="radio" name="color" value="b"> Hitam</label>
            </div>
        <div id="game-info">
            <p>Status: <span id="gameStatus">Mulai permainan!</span></p>
            <button id="newGame">Mulai Permainan Baru</button>
            <button id="flipBoard">Putar Papan</button>
        </div>
    </div>

    <script>
        const boardElement = document.getElementById('chessboard');
        const newGameBtn = document.getElementById('newGame');
        const flipBoardBtn = document.getElementById('flipBoard');
        const gameStatus = document.getElementById('gameStatus');

        const API_URL = 'http://127.0.0.1:5000';
        let game = new Chess();
        let selectedSquare = null;
        let boardFlipped = false;
        let playerColor = 'w';

        const pieceImages = {
            'p': 'black_pawn.png', 'r': 'black_rook.png', 'n': 'black_knight.png', 'b': 'black_bishop.png', 'q': 'black_queen.png', 'k': 'black_king.png',
            'P': 'white_pawn.png', 'R': 'white_rook.png', 'N': 'white_knight.png', 'B': 'white_bishop.png', 'Q': 'white_queen.png', 'K': 'white_king.png'
        };

        // Helper: convert FEN string to 2D board array because newer chess.js versions removed `board()` API
        function fenToBoard(fen) {
            const rows = fen.split(' ')[0].split('/');
            const board = [];
            for (const rowStr of rows) {
                const row = [];
                for (const ch of rowStr) {
                    if (!isNaN(ch)) {
                        // empty squares
                        const empties = parseInt(ch, 10);
                        for (let i = 0; i < empties; i++) row.push(null);
                    } else {
                        const color = ch === ch.toUpperCase() ? 'w' : 'b';
                        const type = ch.toLowerCase();
                        row.push({ color, type });
                    }
                }
                board.push(row);
            }
            return board;
        }

        function drawBoard() {
            boardElement.innerHTML = '';
            const boardData = fenToBoard(game.fen());
            for (let row = 0; row < 8; row++) {
                const boardRow = boardFlipped ? 7 - row : row;
                for (let col = 0; col < 8; col++) {
                    const boardCol = boardFlipped ? 7 - col : col;

                    const square = document.createElement('div');
                    const squareName = 'abcdefgh'[boardCol] + (8 - boardRow);
                    square.id = squareName;
                    square.classList.add('square', (row + col) % 2 === 0 ? 'white' : 'black');

                    const piece = boardData[boardRow][boardCol];
                    if (piece) {
                        const pieceElement = document.createElement('div');
                        pieceElement.classList.add('piece');
                        const symbol = piece.color === 'w' ? piece.type.toUpperCase() : piece.type.toLowerCase();
                        pieceElement.style.backgroundImage = `url(pieces/${pieceImages[symbol]})`;
                        square.appendChild(pieceElement);
                    }

                    // Coordinate labels
                    if (!boardFlipped) {
                        if (col === 0) {
                            const rankEl = document.createElement('span');
                            rankEl.textContent = 8 - row;
                            rankEl.classList.add('coordinate', 'rank');
                            square.appendChild(rankEl);
                        }
                        if (row === 7) {
                            const fileEl = document.createElement('span');
                            fileEl.textContent = 'abcdefgh'[col];
                            fileEl.classList.add('coordinate', 'file');
                            square.appendChild(fileEl);
                        }
                    } else {
                        if (col === 0) {
                            const rankEl = document.createElement('span');
                            rankEl.textContent = row + 1;
                            rankEl.classList.add('coordinate', 'rank');
                            square.appendChild(rankEl);
                        }
                        if (row === 7) {
                            const fileEl = document.createElement('span');
                            fileEl.textContent = 'abcdefgh'[7 - col];
                            fileEl.classList.add('coordinate', 'file');
                            square.appendChild(fileEl);
                        }
                    }
                    boardElement.appendChild(square);
                }
            }
            updateGameStatus();
        }

        function updateGameStatus() {
            if (game.game_over()) {
                if (game.in_checkmate()) {
                    gameStatus.textContent = `Skakmat! ${game.turn() === 'w' ? 'Hitam' : 'Putih'} menang.`;
                } else if (game.in_draw()) {
                    gameStatus.textContent = 'Seri!';
                }
            } else {
                gameStatus.textContent = `Giliran ${game.turn() === 'w' ? 'Putih' : 'Hitam'}`;
            }
        }

        async function makeAiMove() {
            gameStatus.textContent = 'AI sedang berpikir...';
            try {
                const response = await fetch(`${API_URL}/predict_move`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ fen: game.fen(), ai_color: playerColor === 'w' ? 'b' : 'w' })
                });
                const data = await response.json();
                if (data.move && data.move !== 'null') {
                    game.move(data.move, { sloppy: true });
                    drawBoard();
                } else {
                    gameStatus.textContent = 'AI tidak bisa bergerak atau error.';
                }
            } catch (error) {
                console.error('Error fetching AI move:', error);
                gameStatus.textContent = 'Gagal menghubungi engine AI.';
            }
        }

        boardElement.addEventListener('click', async (e) => {
            const squareEl = e.target.closest('.square');
            if (!squareEl) return;

            if (game.turn() !== playerColor) return; // Hanya izinkan pemain bergerak

            if (selectedSquare) {
                const move = game.move({ from: selectedSquare, to: squareEl.id, promotion: 'q' });
                document.getElementById(selectedSquare).classList.remove('selected');
                selectedSquare = null;

                if (move) {
                    drawBoard();
                    if (!game.game_over()) {
                        setTimeout(makeAiMove, 500); // Beri jeda sebelum AI bergerak
                    }
                } 
            } else {
                if (game.get(squareEl.id) && game.get(squareEl.id).color === playerColor) {
                    selectedSquare = squareEl.id;
                    squareEl.classList.add('selected');
                }
            }
        });

        newGameBtn.addEventListener('click', () => {
            playerColor = document.querySelector('input[name="color"]:checked').value;
            game.reset();
            boardFlipped = playerColor === 'b';
            drawBoard();
            if (playerColor === 'b') {
                setTimeout(makeAiMove, 500); // AI (putih) bergerak lebih dulu
            }
        });

        flipBoardBtn.addEventListener('click', () => {
            boardFlipped = !boardFlipped;
            drawBoard();
        });

        // Mulai permainan
        drawBoard();
    </script>
</body>
</html>
