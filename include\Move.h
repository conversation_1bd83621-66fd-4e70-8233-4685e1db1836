#pragma once

#include "ChessTypes.h"
#include <string>

class Move {
private:
    Position from_;
    Position to_;
    MoveType type_;
    PieceType promotionPiece_;
    bool isCapture_;
    
public:
    Move() : from_(-1, -1), to_(-1, -1), type_(MoveType::NORMAL), 
             promotionPiece_(PieceType::NONE), isCapture_(false) {}
    
    Move(const Position& from, const Position& to, MoveType type = MoveType::NORMAL)
        : from_(from), to_(to), type_(type), promotionPiece_(PieceType::NONE), isCapture_(false) {}
    
    Move(const Position& from, const Position& to, PieceType promotion)
        : from_(from), to_(to), type_(MoveType::PAWN_PROMOTION), 
          promotionPiece_(promotion), isCapture_(false) {}
    
    // Getters
    const Position& getFrom() const { return from_; }
    const Position& getTo() const { return to_; }
    MoveType getType() const { return type_; }
    PieceType getPromotionPiece() const { return promotionPiece_; }
    bool isCapture() const { return isCapture_; }
    
    // Setters
    void setCapture(bool capture) { isCapture_ = capture; }
    void setPromotionPiece(PieceType piece) { promotionPiece_ = piece; }
    
    // Utility functions
    bool isValid() const {
        return from_.isValid() && to_.isValid();
    }
    
    bool operator==(const Move& other) const {
        return from_ == other.from_ && to_ == other.to_ && 
               type_ == other.type_ && promotionPiece_ == other.promotionPiece_;
    }
    
    bool operator!=(const Move& other) const {
        return !(*this == other);
    }
    
    // Convert to algebraic notation
    std::string toAlgebraic() const {
        if (!isValid()) return "invalid";
        
        std::string result = from_.toAlgebraic() + to_.toAlgebraic();
        
        if (type_ == MoveType::PAWN_PROMOTION) {
            char promotionChar = pieceToChar(promotionPiece_, Color::WHITE);
            result += std::string(1, std::tolower(promotionChar));
        }
        
        return result;
    }
    
    // Create from algebraic notation (e.g., "e2e4", "e7e8q")
    static Move fromAlgebraic(const std::string& algebraic) {
        if (algebraic.length() < 4) return Move();
        
        Position from = Position::fromAlgebraic(algebraic.substr(0, 2));
        Position to = Position::fromAlgebraic(algebraic.substr(2, 2));
        
        if (!from.isValid() || !to.isValid()) return Move();
        
        Move move(from, to);
        
        // Check for promotion
        if (algebraic.length() == 5) {
            char promotionChar = std::toupper(algebraic[4]);
            switch (promotionChar) {
                case 'Q': move = Move(from, to, PieceType::QUEEN); break;
                case 'R': move = Move(from, to, PieceType::ROOK); break;
                case 'B': move = Move(from, to, PieceType::BISHOP); break;
                case 'N': move = Move(from, to, PieceType::KNIGHT); break;
                default: return Move(); // Invalid promotion
            }
        }
        
        return move;
    }
};
