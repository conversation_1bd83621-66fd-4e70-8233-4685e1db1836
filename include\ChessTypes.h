#pragma once

#include <string>
#include <vector>

// Forward declarations
class ChessBoard;
class Move;

// Enumerations for chess pieces and colors
enum class PieceType {
    NONE = 0,
    PAWN = 1,
    ROOK = 2,
    KNIGHT = 3,
    BISHOP = 4,
    QUEEN = 5,
    KING = 6
};

enum class Color {
    WHITE = 0,
    BLACK = 1,
    NONE = 2
};

// Position on the chess board (0-7 for both rank and file)
struct Position {
    int rank;  // 0-7 (rows, 0 = rank 1, 7 = rank 8)
    int file;  // 0-7 (columns, 0 = file a, 7 = file h)
    
    Position() : rank(0), file(0) {}
    Position(int r, int f) : rank(r), file(f) {}
    
    bool isValid() const {
        return rank >= 0 && rank < 8 && file >= 0 && file < 8;
    }
    
    bool operator==(const Position& other) const {
        return rank == other.rank && file == other.file;
    }
    
    bool operator!=(const Position& other) const {
        return !(*this == other);
    }
    
    // Convert to algebraic notation (e.g., "e4")
    std::string toAlgebraic() const {
        if (!isValid()) return "invalid";
        return std::string(1, 'a' + file) + std::to_string(rank + 1);
    }
    
    // Create from algebraic notation (e.g., "e4")
    static Position fromAlgebraic(const std::string& algebraic) {
        if (algebraic.length() != 2) return Position(-1, -1);
        int file = algebraic[0] - 'a';
        int rank = algebraic[1] - '1';
        return Position(rank, file);
    }
};

// Special move types
enum class MoveType {
    NORMAL = 0,
    CASTLE_KINGSIDE = 1,
    CASTLE_QUEENSIDE = 2,
    EN_PASSANT = 3,
    PAWN_PROMOTION = 4
};

// Piece values for evaluation
const int PIECE_VALUES[] = {
    0,    // NONE
    100,  // PAWN
    500,  // ROOK
    300,  // KNIGHT
    300,  // BISHOP
    900,  // QUEEN
    10000 // KING
};

// Utility functions
inline Color oppositeColor(Color color) {
    return (color == Color::WHITE) ? Color::BLACK : Color::WHITE;
}

inline char pieceToChar(PieceType type, Color color) {
    const char pieces[] = " PRNBQK";
    char c = pieces[static_cast<int>(type)];
    return (color == Color::WHITE) ? c : (c + 32); // lowercase for black
}

inline std::string colorToString(Color color) {
    return (color == Color::WHITE) ? "White" : "Black";
}
