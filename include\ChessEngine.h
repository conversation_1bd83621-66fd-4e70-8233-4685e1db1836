#pragma once

#include "ChessGame.h"
#include "Move.h"
#include "OpeningBook.h"
#include "EndgameTablebase.h"
#include <limits>
#include <unordered_map>
#include <chrono>
#include <functional>
#include <vector>

// Transposition table entry
struct TTEntry
{
    uint64_t hash;
    int depth;
    int score;
    Move bestMove;
    enum Flag
    {
        EXACT,
        LOWER_BOUND,
        UPPER_BOUND
    } flag;

    TTEntry() : hash(0), depth(-1), score(0), flag(EXACT) {}
};

class ChessEngine
{
private:
    int maxDepth_;
    Color engineColor_;

    // Transposition table
    std::unordered_map<uint64_t, TTEntry> transpositionTable_;
    static const int TT_SIZE = 1000000; // 1M entries

    // Time management
    std::chrono::steady_clock::time_point searchStartTime_;
    int maxSearchTimeMs_;
    bool timeUp_;

    // UCI mode flag
    bool quietMode_;

    // UCI callback for sending info during search
    std::function<void(const std::string &)> uciInfoCallback_;

    // Search statistics
    int nodesSearched_;
    int ttHits_;
    int nullMoveCutoffs_;
    int lmrReductions_;
    int multiCutPrunings_;

    // Opening book
    OpeningBook openingBook_;

    // Endgame tablebase
    EndgameTablebase tablebase_;

    // Move ordering
    Move killerMoves_[64][2];     // [depth][slot] - 2 killer moves per depth
    int historyTable_[64][64];    // [from][to] - history heuristic scores
    Move principalVariation_[64]; // PV moves for each depth

    // Multi-move principal variation storage
    std::vector<Move> currentPV_;
    std::vector<Move> bestPVLine_;

    // Advanced search parameters
    bool allowNullMove_;
    int nullMoveReduction_;
    int lmrThreshold_;
    int lmrReduction_;

    // Multi-Cut Pruning parameters
    static const int MULTI_CUT_DEPTH = 3;
    static const int MULTI_CUT_THRESHOLD = 3;

    // Futility Pruning parameters
    static const int FUTILITY_DEPTH = 4;
    static const int FUTILITY_MARGIN_BASE = 100;

    // Tapered Evaluation parameters
    static const int TOTAL_PHASE = 256; // Total game phase value
    static const int PAWN_PHASE = 0;
    static const int KNIGHT_PHASE = 10;
    static const int BISHOP_PHASE = 10;
    static const int ROOK_PHASE = 25;
    static const int QUEEN_PHASE = 90;

    // Piece-square tables for position evaluation
    static const int PAWN_TABLE[8][8];
    static const int KNIGHT_TABLE[8][8];
    static const int BISHOP_TABLE[8][8];
    static const int ROOK_TABLE[8][8];
    static const int QUEEN_TABLE[8][8];
    static const int KING_MIDDLE_GAME_TABLE[8][8];
    static const int KING_END_GAME_TABLE[8][8];

public:
    ChessEngine(Color color = Color::BLACK, int depth = 4);

    // Main AI interface
    Move getBestMove(const ChessGame &game);
    Move getBestMoveWithTime(const ChessGame &game, int timeMs);

    // Evaluation functions
    int evaluatePosition(const ChessBoard &board, Color perspective) const;
    int evaluatePiece(const Piece *piece, const Position &pos, bool isEndGame) const;

    // Advanced evaluation components
    int evaluatePawnStructure(const ChessBoard &board, Color color) const;
    int evaluateKingSafety(const ChessBoard &board, Color color) const;
    int evaluatePieceActivity(const ChessBoard &board, Color color) const;
    int evaluateMobility(const ChessBoard &board, Color color) const;

    // Search functions
    int minimax(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer);
    int principalVariationSearch(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer);
    int quiescenceSearch(ChessGame &game, int alpha, int beta, bool maximizingPlayer);
    Move iterativeDeepening(ChessGame &game, int maxDepth);

    // Utility functions
    void setDepth(int depth) { maxDepth_ = depth; }
    void setColor(Color color) { engineColor_ = color; }
    Color getColor() const { return engineColor_; }
    void setQuietMode(bool quiet) { quietMode_ = quiet; }
    void setUCIInfoCallback(std::function<void(const std::string &)> callback) { uciInfoCallback_ = callback; }

    // PV line functions
    std::string getPVString(int maxMoves = 5) const;
    void collectPVLine(ChessGame &game, int depth, std::vector<Move> &pvLine);

    // Search statistics
    int getNodesSearched() const { return nodesSearched_; }
    int getTTHits() const { return ttHits_; }
    void clearTT() { transpositionTable_.clear(); }

    // Analysis functions
    std::string getAnalysisString() const;

    // Opening book functions
    bool hasOpeningMove(const ChessGame &game) const;
    Move getOpeningMove(const ChessGame &game) const;

private:
    bool isEndGame(const ChessBoard &board) const;
    int getPieceSquareValue(PieceType type, const Position &pos, Color color, bool isEndGame) const;
    std::vector<Move> orderMoves(const std::vector<Move> &moves, const ChessGame &game) const;
    std::vector<Move> orderMovesAdvanced(const std::vector<Move> &moves, const ChessGame &game, int depth, const Move &ttMove) const;

    // Move ordering helpers
    int getMoveScore(const Move &move, const ChessGame &game, int depth, const Move &ttMove) const;
    void updateKillerMoves(const Move &move, int depth);
    void updateHistoryTable(const Move &move, int depth);

    // Quiescence search helpers
    std::vector<Move> generateCaptures(const ChessGame &game) const;
    bool isQuiet(const Move &move, const ChessGame &game) const;
    int staticExchangeEvaluation(const ChessGame &game, const Move &move) const;

    // Advanced search techniques
    bool canDoNullMove(const ChessGame &game, bool maximizingPlayer) const;
    int nullMoveSearch(ChessGame &game, int depth, int beta, bool maximizingPlayer);
    bool shouldReduceMove(const Move &move, int moveIndex, int depth, bool inCheck) const;
    int getReduction(int depth, int moveIndex) const;

    // Multi-Cut Pruning
    bool shouldDoMultiCut(int depth, int cutoffCount) const;

    // Futility Pruning
    bool canPruneFutility(int depth, int alpha, int staticEval, bool inCheck) const;
    int getFutilityMargin(int depth) const;

    // Tapered Evaluation
    int calculateGamePhase(const ChessBoard &board) const;
    int taperedEval(int mgScore, int egScore, int phase) const;

    // Transposition table methods
    uint64_t generateHash(const ChessBoard &board) const;
    void storeTT(uint64_t hash, int depth, int score, const Move &bestMove, TTEntry::Flag flag);
    bool probeTT(uint64_t hash, int depth, int alpha, int beta, int &score, Move &bestMove);

    // Time management
    bool isTimeUp() const;
    void resetSearchStats();

    // Evaluation helpers
    bool isPawnIsolated(const ChessBoard &board, const Position &pawnPos) const;
    bool isPawnDoubled(const ChessBoard &board, const Position &pawnPos) const;
    bool isPawnPassed(const ChessBoard &board, const Position &pawnPos) const;
    int countAttackers(const ChessBoard &board, const Position &pos, Color attackingColor) const;
    std::vector<Position> getKingZone(const Position &kingPos) const;
};
