#pragma once

#include "ChessTypes.h"
#include "Move.h"
#include "ChessBoard.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <fstream>
#include <random>

struct BookEntry {
    Move move;
    uint16_t weight;
    uint32_t learn;
    
    BookEntry() : weight(0), learn(0) {}
    BookEntry(const Move& m, uint16_t w, uint32_t l) : move(m), weight(w), learn(l) {}
};

class ImprovedOpeningBook {
private:
    std::unordered_map<uint64_t, std::vector<BookEntry>> bookMoves_;
    std::mt19937 rng_;
    bool loaded_;
    
    // Polyglot book format structures
    struct PolyglotEntry {
        uint64_t key;
        uint16_t move;
        uint16_t weight;
        uint32_t learn;
    };
    
    // Convert Polyglot move format to our Move format
    Move polyglotToMove(uint16_t polyMove, const ChessBoard& board) const {
        int from = (polyMove >> 6) & 63;
        int to = polyMove & 63;
        int promotion = (polyMove >> 12) & 7;
        
        Position fromPos(from / 8, from % 8);
        Position toPos(to / 8, to % 8);
        
        MoveType moveType = MoveType::NORMAL;
        PieceType promotionPiece = PieceType::NONE;
        
        // Check for special moves
        const Piece* piece = board.getPiece(fromPos);
        if (piece) {
            // Check for castling
            if (piece->getType() == PieceType::KING) {
                if (abs(toPos.file - fromPos.file) == 2) {
                    moveType = (toPos.file > fromPos.file) ? 
                              MoveType::CASTLE_KINGSIDE : MoveType::CASTLE_QUEENSIDE;
                }
            }
            // Check for en passant
            else if (piece->getType() == PieceType::PAWN) {
                if (abs(toPos.file - fromPos.file) == 1 && !board.getPiece(toPos)) {
                    moveType = MoveType::EN_PASSANT;
                }
                // Check for promotion
                else if ((piece->getColor() == Color::WHITE && toPos.rank == 7) ||
                        (piece->getColor() == Color::BLACK && toPos.rank == 0)) {
                    moveType = MoveType::PAWN_PROMOTION;
                    switch (promotion) {
                        case 1: promotionPiece = PieceType::KNIGHT; break;
                        case 2: promotionPiece = PieceType::BISHOP; break;
                        case 3: promotionPiece = PieceType::ROOK; break;
                        case 4: promotionPiece = PieceType::QUEEN; break;
                        default: promotionPiece = PieceType::QUEEN; break;
                    }
                }
            }
        }
        
        return Move(fromPos, toPos, moveType, promotionPiece);
    }
    
    // Generate Polyglot-compatible hash
    uint64_t generatePolyglotHash(const ChessBoard& board, Color sideToMove,
                                 int castlingRights, int enPassantFile) const {
        // This is a simplified version - real Polyglot hash is more complex
        uint64_t hash = 0ULL;
        
        // Hash pieces (simplified)
        for (int rank = 0; rank < 8; ++rank) {
            for (int file = 0; file < 8; ++file) {
                const Piece* piece = board.getPiece(Position(rank, file));
                if (piece) {
                    int square = rank * 8 + file;
                    int pieceIndex = static_cast<int>(piece->getType()) - 1;
                    int colorIndex = static_cast<int>(piece->getColor());
                    hash ^= (uint64_t(pieceIndex + colorIndex * 6) << square);
                }
            }
        }
        
        // Add side to move
        if (sideToMove == Color::BLACK) {
            hash ^= 0x1ULL;
        }
        
        return hash;
    }

public:
    ImprovedOpeningBook() : rng_(std::random_device{}()), loaded_(false) {}
    
    bool loadFromFile(const std::string& filename) {
        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        bookMoves_.clear();
        
        PolyglotEntry entry;
        while (file.read(reinterpret_cast<char*>(&entry), sizeof(PolyglotEntry))) {
            // Convert from big-endian if necessary
            uint64_t key = __builtin_bswap64(entry.key);
            uint16_t move = __builtin_bswap16(entry.move);
            uint16_t weight = __builtin_bswap16(entry.weight);
            uint32_t learn = __builtin_bswap32(entry.learn);
            
            // We'll need to convert the move when we use it
            BookEntry bookEntry;
            bookEntry.weight = weight;
            bookEntry.learn = learn;
            
            bookMoves_[key].push_back(bookEntry);
        }
        
        loaded_ = true;
        return true;
    }
    
    Move getBookMove(const ChessBoard& board, Color sideToMove, 
                    int castlingRights = 0, int enPassantFile = -1) {
        if (!loaded_) {
            return Move(); // Invalid move
        }
        
        uint64_t hash = generatePolyglotHash(board, sideToMove, castlingRights, enPassantFile);
        
        auto it = bookMoves_.find(hash);
        if (it == bookMoves_.end() || it->second.empty()) {
            return Move(); // No book move found
        }
        
        const std::vector<BookEntry>& entries = it->second;
        
        // Calculate total weight
        uint32_t totalWeight = 0;
        for (const BookEntry& entry : entries) {
            totalWeight += entry.weight;
        }
        
        if (totalWeight == 0) {
            return Move(); // No valid moves
        }
        
        // Select move based on weight
        uint32_t randomValue = rng_() % totalWeight;
        uint32_t currentWeight = 0;
        
        for (const BookEntry& entry : entries) {
            currentWeight += entry.weight;
            if (randomValue < currentWeight) {
                return entry.move;
            }
        }
        
        // Fallback to first move
        return entries[0].move;
    }
    
    bool hasBookMove(const ChessBoard& board, Color sideToMove,
                    int castlingRights = 0, int enPassantFile = -1) const {
        if (!loaded_) {
            return false;
        }
        
        uint64_t hash = generatePolyglotHash(board, sideToMove, castlingRights, enPassantFile);
        auto it = bookMoves_.find(hash);
        return it != bookMoves_.end() && !it->second.empty();
    }
    
    size_t getBookSize() const {
        return bookMoves_.size();
    }
    
    bool isLoaded() const {
        return loaded_;
    }
    
    // Get multiple book moves with their weights for analysis
    std::vector<std::pair<Move, uint16_t>> getBookMoves(const ChessBoard& board, Color sideToMove,
                                                       int castlingRights = 0, int enPassantFile = -1) {
        std::vector<std::pair<Move, uint16_t>> result;
        
        if (!loaded_) {
            return result;
        }
        
        uint64_t hash = generatePolyglotHash(board, sideToMove, castlingRights, enPassantFile);
        auto it = bookMoves_.find(hash);
        
        if (it != bookMoves_.end()) {
            for (const BookEntry& entry : it->second) {
                result.emplace_back(entry.move, entry.weight);
            }
        }
        
        return result;
    }
};
