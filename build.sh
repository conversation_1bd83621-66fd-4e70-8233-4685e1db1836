#!/bin/bash

# VibeChess Engine Build Script
# Builds all components of the chess engine

echo "=== VibeChess Engine Build Script ==="
echo

# Check if g++ is available
if ! command -v g++ &> /dev/null; then
    echo "Error: g++ compiler not found!"
    echo "Please install a C++17 compatible compiler."
    exit 1
fi

# Create build directory if it doesn't exist
mkdir -p build

echo "Building VibeChess Engine components..."
echo

# Build UCI engine (main target for GUIs)
echo "1. Building UCI Engine..."
if g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci 2>/dev/null; then
    echo "   ✓ chess_engine_uci built successfully"
else
    echo "   ✗ Failed to build UCI engine"
    exit 1
fi

# Build console version
echo "2. Building Console Version..."
if g++ -std=c++17 -Iinclude -O2 main.cpp src/*.cpp -o chess_engine 2>/dev/null; then
    echo "   ✓ chess_engine built successfully"
else
    echo "   ✗ Failed to build console version"
    exit 1
fi

# Build configuration tool
echo "3. Building Configuration Tool..."
if g++ -std=c++17 -Iinclude -O2 -c src/EngineConfig.cpp -o build/EngineConfig.o 2>/dev/null && \
   g++ -std=c++17 -Iinclude -O2 -c src/EngineConfigUI.cpp -o build/EngineConfigUI.o 2>/dev/null; then
    
    # Create a simple config main since we removed the original
    cat > build/simple_config_main.cpp << 'EOF'
#include "EngineConfig.h"
#include <iostream>

int main(int argc, char* argv[]) {
    EngineConfig config;
    
    if (argc > 1) {
        std::string arg = argv[1];
        if (arg == "--show") {
            std::cout << config.getAllParametersString();
        } else if (arg == "--uci") {
            std::cout << config.getUCIOptionsString();
        } else if (arg == "--save" && argc > 2) {
            if (config.saveToFile(argv[2])) {
                std::cout << "Configuration saved to " << argv[2] << std::endl;
            } else {
                std::cout << "Failed to save configuration" << std::endl;
            }
        } else if (arg == "--load" && argc > 2) {
            if (config.loadFromFile(argv[2])) {
                std::cout << "Configuration loaded from " << argv[2] << std::endl;
                std::cout << config.getAllParametersString();
            } else {
                std::cout << "Failed to load configuration" << std::endl;
            }
        } else {
            std::cout << "Usage: " << argv[0] << " [--show|--uci|--save file|--load file]" << std::endl;
        }
    } else {
        std::cout << "VibeChess Engine Configuration" << std::endl;
        std::cout << "Usage: " << argv[0] << " [--show|--uci|--save file|--load file]" << std::endl;
    }
    
    return 0;
}
EOF
    
    if g++ -std=c++17 -Iinclude -O2 build/simple_config_main.cpp build/EngineConfig.o -o engine_config 2>/dev/null; then
        echo "   ✓ engine_config built successfully"
    else
        echo "   ✗ Failed to build configuration tool"
    fi
else
    echo "   ✗ Failed to compile configuration components"
fi

echo
echo "=== Build Summary ==="
echo "Built executables:"

if [ -f "chess_engine_uci" ] || [ -f "chess_engine_uci.exe" ]; then
    echo "  ✓ chess_engine_uci    - UCI protocol engine (for GUIs)"
fi

if [ -f "chess_engine" ] || [ -f "chess_engine.exe" ]; then
    echo "  ✓ chess_engine        - Console version"
fi

if [ -f "engine_config" ] || [ -f "engine_config.exe" ]; then
    echo "  ✓ engine_config       - Configuration tool"
fi

echo
echo "=== Usage ==="
echo "For n Croissant GUI:"
echo "  Use: chess_engine_uci (or chess_engine_uci.exe on Windows)"
echo
echo "For console play:"
echo "  Run: ./chess_engine"
echo
echo "For configuration:"
echo "  Run: ./engine_config --show"
echo "       ./engine_config --uci"
echo "       ./engine_config --save config.ini"
echo
echo "Build completed successfully!"
