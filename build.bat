@echo off
REM VibeChess Engine Build Script for Windows
REM Builds all components of the chess engine

echo === VibeChess Engine Build Script ===
echo.

REM Check if g++ is available
g++ --version >nul 2>&1
if errorlevel 1 (
    echo Error: g++ compiler not found!
    echo Please install MinGW-w64 or similar C++17 compatible compiler.
    pause
    exit /b 1
)

REM Create build directory if it doesn't exist
if not exist build mkdir build

echo Building VibeChess Engine components...
echo.

REM Build UCI engine (main target for GUIs)
echo 1. Building UCI Engine...
g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci.exe >nul 2>&1
if errorlevel 1 (
    echo    X Failed to build UCI engine
    pause
    exit /b 1
) else (
    echo    √ chess_engine_uci.exe built successfully
)

REM Build console version
echo 2. Building Console Version...
g++ -std=c++17 -Iinclude -O2 main.cpp src/*.cpp -o chess_engine.exe >nul 2>&1
if errorlevel 1 (
    echo    X Failed to build console version
    pause
    exit /b 1
) else (
    echo    √ chess_engine.exe built successfully
)

REM Build configuration tool
echo 3. Building Configuration Tool...
g++ -std=c++17 -Iinclude -O2 -c src/EngineConfig.cpp -o build/EngineConfig.o >nul 2>&1
g++ -std=c++17 -Iinclude -O2 -c src/EngineConfigUI.cpp -o build/EngineConfigUI.o >nul 2>&1

REM Create simple config main
echo #include "EngineConfig.h" > build\simple_config_main.cpp
echo #include ^<iostream^> >> build\simple_config_main.cpp
echo. >> build\simple_config_main.cpp
echo int main(int argc, char* argv[]) { >> build\simple_config_main.cpp
echo     EngineConfig config; >> build\simple_config_main.cpp
echo     if (argc ^> 1) { >> build\simple_config_main.cpp
echo         std::string arg = argv[1]; >> build\simple_config_main.cpp
echo         if (arg == "--show") { >> build\simple_config_main.cpp
echo             std::cout ^<^< config.getAllParametersString(); >> build\simple_config_main.cpp
echo         } else if (arg == "--uci") { >> build\simple_config_main.cpp
echo             std::cout ^<^< config.getUCIOptionsString(); >> build\simple_config_main.cpp
echo         } else if (arg == "--save" ^&^& argc ^> 2) { >> build\simple_config_main.cpp
echo             if (config.saveToFile(argv[2])) { >> build\simple_config_main.cpp
echo                 std::cout ^<^< "Configuration saved to " ^<^< argv[2] ^<^< std::endl; >> build\simple_config_main.cpp
echo             } else { >> build\simple_config_main.cpp
echo                 std::cout ^<^< "Failed to save configuration" ^<^< std::endl; >> build\simple_config_main.cpp
echo             } >> build\simple_config_main.cpp
echo         } else if (arg == "--load" ^&^& argc ^> 2) { >> build\simple_config_main.cpp
echo             if (config.loadFromFile(argv[2])) { >> build\simple_config_main.cpp
echo                 std::cout ^<^< "Configuration loaded from " ^<^< argv[2] ^<^< std::endl; >> build\simple_config_main.cpp
echo                 std::cout ^<^< config.getAllParametersString(); >> build\simple_config_main.cpp
echo             } else { >> build\simple_config_main.cpp
echo                 std::cout ^<^< "Failed to load configuration" ^<^< std::endl; >> build\simple_config_main.cpp
echo             } >> build\simple_config_main.cpp
echo         } else { >> build\simple_config_main.cpp
echo             std::cout ^<^< "Usage: " ^<^< argv[0] ^<^< " [--show|--uci|--save file|--load file]" ^<^< std::endl; >> build\simple_config_main.cpp
echo         } >> build\simple_config_main.cpp
echo     } else { >> build\simple_config_main.cpp
echo         std::cout ^<^< "VibeChess Engine Configuration" ^<^< std::endl; >> build\simple_config_main.cpp
echo         std::cout ^<^< "Usage: " ^<^< argv[0] ^<^< " [--show|--uci|--save file|--load file]" ^<^< std::endl; >> build\simple_config_main.cpp
echo     } >> build\simple_config_main.cpp
echo     return 0; >> build\simple_config_main.cpp
echo } >> build\simple_config_main.cpp

g++ -std=c++17 -Iinclude -O2 build\simple_config_main.cpp build\EngineConfig.o -o engine_config.exe >nul 2>&1
if errorlevel 1 (
    echo    X Failed to build configuration tool
) else (
    echo    √ engine_config.exe built successfully
)

echo.
echo === Build Summary ===
echo Built executables:

if exist chess_engine_uci.exe (
    echo   √ chess_engine_uci.exe - UCI protocol engine (for GUIs)
)

if exist chess_engine.exe (
    echo   √ chess_engine.exe     - Console version
)

if exist engine_config.exe (
    echo   √ engine_config.exe    - Configuration tool
)

echo.
echo === Usage ===
echo For n Croissant GUI:
echo   Use: chess_engine_uci.exe
echo.
echo For console play:
echo   Run: chess_engine.exe
echo.
echo For configuration:
echo   Run: engine_config.exe --show
echo        engine_config.exe --uci
echo        engine_config.exe --save config.ini
echo.
echo Build completed successfully!
echo.
pause
